import CTA from "@/components/CTA";
import Testimonials from "@/components/Testimonials";
import TrustIndicators from "@/components/TrustIndicators";
import FAQ from "@/components/FAQ";
import YouTubeEmbed from "@/components/YouTubeEmbed";
import Link from "next/link";
import { Waves, TreePine, Home as HomeIcon, Link as LinkIcon, DoorOpen, Shield, Wrench, Building, Settings, Gem, Calculator, BadgeCheck, Award, Handshake, Heart, ShieldCheck, CheckCircle } from 'lucide-react';
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "GTA Fencing Company - Professional Fence Installation & Repair Services in Toronto",
  description: "GTA Fencing Company is Toronto's premier fence contractor offering professional installation, repair, and maintenance services. Serving the Greater Toronto Area with quality fencing solutions for residential and commercial properties.",
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: "https://www.gtafencingcompany.com",
  },
};

export default function Home() {
  return (
    <>
      {/* Schema Markup */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "http://schema.org",
            "@type": "GeneralContractor",
            "url": "https://www.gtafencingcompany.com",
            "@id": "https://gtafencingcompany.s3.amazonaws.com/index.html",
            "name": "GTA Fencing Company",
            "alternateName": "GTA Fencing Your Fence Contractor",
            "description": "GTA Fencing is one of the top fence contractor in Toronto. We offer high quality fence installation services at affordable prices. Our team of professional installers can handle any fencing project, big or small including custom fences, gates, decking, landscaping, and more.",
            "disambiguatingdescription": "GTA Fencing is one of the leading Toronto fence companies. We offer reasonably priced fence installation services of superior quality. Our team of professional installers can complete any fence intallation and fence repair project, no matter how large or small, including custom fences, gates, decking, and landscaping.",
            "mainEntityOfPage": "https://www.gtafencingcompany.com",
            "logo": "https://www.gtafencingcompany.com/wp-content/uploads/2022/09/GTA-Fencing-Logo.png",
            "image": "https://www.gtafencingcompany.com/wp-content/uploads/2022/09/GTA-Fencing-Company-Toronto-Header-Background-1920.jpg",
            "sameAs": [
              "https://fence-installation-toronto.business.site/",
              "https://sites.google.com/view/fence-installation-contractor/",
              "https://www.google.com/maps/d/viewer?mid=1pg9w05PGts239ZzrTWuOETvGLYyF4E4&ll=43.68190618886119,-79.**************&z=11",
              "https://www.youtube.com/@gtafencecompany",
              "https://www.facebook.com/GTA-Fencing-109068478589630",
              "https://twitter.com/FencingGta",
              "https://soundcloud.com/social-gta-fencing",
              "https://gtafencing.weebly.com/",
              "https://medium.com/@gtafencing",
              "https://www.tumblr.com/blog/gtafencing",
              "https://www.pinterest.com/gtafencing/",
              "https://www.pearltrees.com/gtafencing"
            ],
            "additionalType": [
              "https://www.google.com/search?kgmid=/m/04s84y",
              "https://en.wikipedia.org/wiki/General_contractor",
              "",
              "",
              ""
            ],
            "knowsAbout": [
              "https://www.google.com/search?kgmid=/m/0blz9",
              "https://www.google.com/search?kgmid=/m/04s84y",
              "https://www.google.com/search?kgmid=/m/0cd3bj",
              "https://www.google.com/search?kgmid=/m/03q13l",
              "https://www.google.com/search?kgmid=/m/04yzhv",
              "https://www.google.com/search?kgmid=/m/03n2_q",
              "https://www.google.com/search?kgmid=/g/121mknrx",
              ""
            ],
            "currenciesAccepted": ["CAD", "USD"],
            "paymentAccepted": "Cash, Visa, Mastercard, American Express, Interac Debit, Apple Pay",
            "openingHours": "Mo-Fr 08:00-17:00",
            "areaServed": {
              "@type": "AdministrativeArea",
              "@id": "https://www.wikidata.org/wiki/Q1024624",
              "name": "Greater Toronto Area"
            },
            "priceRange": "$$$",
            "hasMap": "https://www.google.com/maps/d/viewer?mid=1pg9w05PGts239ZzrTWuOETvGLYyF4E4&ll=43.68190618886119,-79.**************&z=11",
            "email": "<EMAIL>",
            "telephone": "+1************",
            "address": {
              "@type": "PostalAddress",
              "streetAddress": "68 Tycos Dr (Unit 1)",
              "addressLocality": "North York",
              "addressRegion": "ON",
              "postalCode": "M6B 1V9",
              "addressCountry": "CAN"
            },
            "geo": {
              "@type": "GeoCoordinates",
              "latitude": "43.7182412",
              "longitude": "-79.3780581"
            }
          })
        }}
      />

      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "http://schema.org",
            "@type": "VideoObject",
            "accessMode": [
              "auditory",
              "visual",
              "textual",
              "textOnVisual"
            ],
            "accessibilityFeature": [
              "audioDescription",
              "captions"
            ],
            "additionalType": "https://en.wikipedia.org/wiki/Video",
            "name": "GTA Fencing - The Best Fence Contractor in Toronto",
            "contentUrl": "https://www.youtube.com/watch?v=eA0S0Vho3G8",
            "embedUrl": "https://www.youtube.com/embed/eA0S0Vho3G8",
            "thumbnailUrl": "https://i3.ytimg.com/vi/eA0S0Vho3G8/maxresdefault.jpg",
            "duration": "PT1M18S",
            "uploadDate": "2022-09-19",
            "mainEntityOfPage": "GTA Fencing Company",
            "alternateName": "The Best Fence Company in Toronto - GTA Fencing",
            "description": "We can help you whether you're building a fence for your own home or putting up a fence for a business. We've been putting up fences in Toronto for over 20 years, so we can guarantee quality work and customer satisfaction. Your fencing project is the most important thing to us. We take the time to learn about your needs and give you a solution that is made just for you and will last for years. We'll take care of everything, from preparing the site to installing it, so you can rest easy knowing that your project is in good hands. When you hire GTA Fencing, you're hiring experts, so you can focus on more important things, like enjoying your new garden or relaxing by the pool. And because we know how important your fence is to you, we do everything we can to make sure every job is done quickly and well. We'll always show up on time, finish on time, and only use the best materials on the market. Professional fencing of high quality. We can help you build something that fits your exact needs, whether you want privacy, security, or both. We have a wide range of materials to choose from, such as wood, vinyl, aluminum, chain link, and more. We are based in Toronto, so we know how important security is for homeowners. They will be respectful and professional with your property, and they will be able to handle anything that comes up. We've been making good fences and gates since 2005, whether you want a simple privacy screen or something more complicated. We know how to get the job done right. A Reasonable Price. Our goal is simple: to sell high-quality items at prices that are competitive. We can help you make a custom fence design that fits your needs and budget, whether you're building a new house or fixing up an old one. So, if you're looking for a trustworthy fencing company in Toronto, GTA Fencing is the place to go.",
            "disambiguatingDescription": "GTA Fencing can build a home or business fence. Over 20 years of experience installing fences in Toronto ensures quality work and customer satisfaction. We prioritize your fencing project. We learn about your needs and provide a long-lasting solution. We'll handle everything, from site prep to installation, so you can relax. When you hire GTA Fencing, you're hiring experts so you can enjoy your garden or pool. We work quickly and efficiently because we know how important your fence is. We'll always be on time and use the best materials. High-quality fencers. If you want privacy, security, or both, we can help. Wood, vinyl, aluminum, chain link, and more are available.Toronto-based, we understand the importance of home security. They'll treat your property with respect and handle any issues that arise. Since 2005, we've made good fences and gates, from simple to complex. We do good work. Cost-effective. We sell high-quality items at competitive prices. Whether you're building a new home or remodeling an old one, we can help you design a custom fence. GTA Fencing is a reputable Toronto fencing company.",
            "locationCreated": {
              "@type": "Place",
              "@id": "kg:/m/01bjv",
              "geo": {
                "@type": "GeoCoordinates",
                "latitude": 43.7182412,
                "longitude": -79.3780581
              },
              "name": "Toronto",
              "photo": {
                "@type": "ImageObject",
                "@id": "https://www.gtafencingcompany.com/#image",
                "url": "https://www.gtafencingcompany.com/wp-content/uploads/2022/09/GTA-Fencing-Company-Toronto-Header-Background-1920.jpg",
                "additionalType": [
                  "https://en.wikipedia.org/wiki/Fence",
                  "https://en.wikipedia.org/wiki/Toronto",
                  "https://en.wikipedia.org/wiki/Greater_Toronto_Area",
                  "https://en.wikipedia.org/wiki/Greater_Toronto_Area",
                  "https://trends.google.com/trends/explore?geo=CA&q=%2Fm%2F0blz9",
                  "https://news.google.com/search?q=fence+contractor+toronto&hl=en-CA&gl=CA&ceid=CA:en",
                  "https://www.wikidata.org/wiki/Q148571"
                ],
                "alternateName": "GTA Fencing Company Toronto",
                "description": "GTA Fencing is a top fence company. We provide affordable, high-quality fencing. Our team can install custom fences, gates, decking, landscaping, and more. Quotes available now.",
                "disambiguatingDescription": "GTA Fencing is one of the best Toronto fence builders. We do good work on fences at prices that are easy on the wallet. Our team of professional fence installers can do any job, big or small, including custom fences, gates, decking, landscaping, and more.",
                "name": "GTA Fencing - The Best Fence Contractor in Toronto",
                "sameAs": [
                  "https://drive.google.com/drive/folders/1Aqv-23wWZQQEBAGpQcp9h0A2xBdga24o",
                  "https://lh3.googleusercontent.com/p/AF1QipOSQaBG3dKva6-k-gMICieTPmLgM_HZdMjHWB8P=w600-h0",
                  "https://www.youtube.com/watch?v=eA0S0Vho3G8"
                ]
              }
            },
            "publisher": {
              "@type": "Organization",
              "alternateName": "GTA Fencing",
              "description": "GTA Fencing is one of the top fence contractors in Toronto. We offer high quality fencing services at affordable prices. Our team of professional installers can handle any fencing project, big or small including custom fences, gates, decking, landscaping, and more.",
              "logo": "https://www.gtafencingcompany.com/wp-content/uploads/2022/09/GTA-Fencing-Logo.png",
              "name": "GTA Fencing Company",
              "url": "https://www.gtafencingcompany.com/",
              "sameAs": [
                "https://sites.google.com/view/fence-installation-contractor/",
                "https://fence-installation-toronto.business.site/",
                "https://www.youtube.com/channel/UCTKAvstAlMaUYrMWh7oOCQA",
                "https://twitter.com/FencingGta",
                "https://www.facebook.com/GTA-Fencing-109068478589630",
                "https://www.pinterest.com/gtafencing/",
                "https://medium.com/@gtafencing",
                "https://soundcloud.com/social-gta-fencing"
              ]
            }
          })
        }}
      />

      <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-deep-navy text-white text-center py-20">
        <div className="container mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Your Premier Fence Contractor in the Greater Toronto Area</h1>
          <p className="text-lg md:text-xl mb-8 max-w-3xl mx-auto">
            As the GTA&apos;s leading fencing specialists, GTA Fencing Company provides expert installation, repair, and maintenance for homeowners and businesses across Toronto, Mississauga, Brampton, and the surrounding areas. We specialize in a wide range of fencing solutions designed for Southern Ontario&apos;s climate. Get your free, no-obligation quote today.
          </p>
          <CTA text="Get Your Free Quote" link="/contact" />
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-warm-off-white">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold text-deep-navy mb-8">Our Fencing Services</h2>
          <p className="text-lg text-rich-charcoal mb-8 max-w-2xl mx-auto">
            This section lists and links to all primary service pages, establishing the breadth of expertise.
          </p>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8">
            <Link href="/services/pool-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <Waves size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Pool Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/wood-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <TreePine size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Wood Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/vinyl-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <HomeIcon size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Vinyl Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/chain-link-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <LinkIcon size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Chain Link Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/wrought-iron-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <DoorOpen size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Wrought Iron Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/perimeter-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <Shield size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Perimeter Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/security-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <Shield size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Security Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/industrial-chain-link-fencing" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <Building size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Industrial Chain Link Fencing</h3>
              </div>
            </Link>
            
            <Link href="/services/fence-maintenance" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <Settings size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Fence Maintenance</h3>
              </div>
            </Link>
            
            <Link href="/services/fence-repair-services" className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 group hover:scale-105">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-3 group-hover:border-opacity-50 transition-all duration-300">
                  <Wrench size={24} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="font-bold text-rich-charcoal group-hover:text-premium-gold transition-colors">Fence Repair Services</h3>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* Locations Section */}
      <section className="py-16">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-bold text-deep-navy mb-8">Serving the Greater Toronto Area and Surrounding Communities</h2>
          <p className="text-lg text-rich-charcoal mb-8 max-w-2xl mx-auto">
            This section lists and links to all primary location pages, establishing local relevance.
          </p>
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {['Toronto', 'Vaughan', 'Richmond Hill', 'Markham', 'Mississauga', 'Brampton', 'Oakville', 'Burlington', 'Whitby', 'Oshawa'].map(location => (
              <Link key={location} href={`/locations/${location.toLowerCase()}`} className="bg-soft-beige px-4 py-2 rounded-full text-deep-navy font-semibold hover:bg-premium-gold hover:text-white transition-colors">
                {location}
              </Link>
            ))}
          </div>
          
          {/* Local SEO Enhancement */}
          <div className="bg-soft-beige p-6 rounded-lg max-w-4xl mx-auto">
            <h3 className="text-xl font-bold text-deep-navy mb-4">Proudly Serving Southern Ontario</h3>
            <p className="text-rich-charcoal">
              From the bustling streets of downtown Toronto to the quiet neighborhoods of Oakville, we bring professional fencing solutions to every corner of the Greater Toronto Area. Our local expertise means we understand the unique challenges of Ontario weather and municipal requirements.
            </p>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 bg-soft-beige">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-deep-navy text-center mb-8">Why Choose GTA Fencing Company?</h2>
          <p className="text-lg text-rich-charcoal text-center mb-12 max-w-2xl mx-auto">
            This section leverages the defined trust-building elements to build confidence and reduce user anxiety.
          </p>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center flex flex-col items-center group">
              <div className="w-16 h-16 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-4 group-hover:border-opacity-50 transition-all duration-300">
                <Settings size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
              </div>
              <h3 className="text-xl font-bold text-rich-charcoal mb-2">Expert Installation</h3>
              <p className="text-rich-charcoal">Our certified team ensures every fence is built to last, meeting all local codes and standards.</p>
            </div>
            <div className="text-center flex flex-col items-center group">
              <div className="w-16 h-16 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-4 group-hover:border-opacity-50 transition-all duration-300">
                <Gem size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
              </div>
              <h3 className="text-xl font-bold text-rich-charcoal mb-2">Quality Materials and Workmanship</h3>
              <p className="text-rich-charcoal">We source premium materials and stand behind our work, guaranteeing a durable and beautiful result.</p>
            </div>
            <div className="text-center flex flex-col items-center group">
              <div className="w-16 h-16 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-4 group-hover:border-opacity-50 transition-all duration-300">
                <Calculator size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
              </div>
              <h3 className="text-xl font-bold text-rich-charcoal mb-2">Free, No-Obligation Quotes</h3>
              <p className="text-rich-charcoal">We provide clear, detailed estimates with no hidden fees, helping you make an informed decision.</p>
            </div>
            <div className="text-center flex flex-col items-center group">
              <div className="w-16 h-16 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mb-4 group-hover:border-opacity-50 transition-all duration-300">
                <BadgeCheck size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
              </div>
              <h3 className="text-xl font-bold text-rich-charcoal mb-2">Licensed and Insured</h3>
              <p className="text-rich-charcoal">Work with confidence knowing our team is fully licensed and insured for your complete protection.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Video Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-deep-navy mb-4">See Our Work in Action</h2>
            <p className="text-lg text-rich-charcoal max-w-2xl mx-auto">
              Watch how GTA Fencing Company transforms properties across the Greater Toronto Area with professional fence installation and superior craftsmanship.
            </p>
          </div>
          <div className="max-w-4xl mx-auto">
            <YouTubeEmbed
              videoId="eA0S0Vho3G8"
              title="GTA Fencing - The Best Fence Contractor in Toronto"
              className="shadow-2xl"
            />
          </div>
        </div>
      </section>

      {/* Trust Indicators Section */}
      <TrustIndicators />

      {/* Testimonials Section */}
      <Testimonials />

      {/* Story Section */}
      <section className="py-16">
        <div className="container mx-auto text-center max-w-4xl">
          <h2 className="text-3xl font-bold text-deep-navy mb-6">Our Story of Quality and Commitment</h2>
          <div className="text-left space-y-6">
            <p className="text-lg text-rich-charcoal leading-relaxed">
              Founded in 2009, GTA Fencing Company began with a simple mission: to provide the Greater Toronto Area with exceptional fencing solutions that combine quality craftsmanship with outstanding customer service. What started as a small family business has grown into the region&apos;s most trusted fencing contractor.
            </p>
            <p className="text-lg text-rich-charcoal leading-relaxed">
              Our commitment to the local community runs deep. We&apos;ve helped thousands of homeowners create safe, beautiful outdoor spaces while supporting local businesses and contributing to the economic growth of the GTA. Every fence we install is a testament to our dedication to quality and our investment in the communities we serve.
            </p>
            <p className="text-lg text-rich-charcoal leading-relaxed">
              Today, we continue to uphold the values that built our reputation: integrity, craftsmanship, and an unwavering commitment to customer satisfaction. When you choose GTA Fencing Company, you&apos;re not just getting a fence – you&apos;re partnering with a local business that truly cares about your project and your community.
            </p>
          </div>
          
          {/* Company Values */}
          <div className="grid md:grid-cols-3 gap-6 mt-12">
            <div className="bg-soft-beige p-6 rounded-lg group hover:shadow-lg transition-all duration-300">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 border-2 border-premium-gold border-opacity-40 rounded-full flex items-center justify-center mr-3 group-hover:border-opacity-60 transition-all duration-300">
                  <Award size={20} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="text-xl font-bold text-deep-navy">Excellence</h3>
              </div>
              <p className="text-rich-charcoal">We never compromise on quality, using only the finest materials and proven installation techniques.</p>
            </div>
            <div className="bg-soft-beige p-6 rounded-lg group hover:shadow-lg transition-all duration-300">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 border-2 border-premium-gold border-opacity-40 rounded-full flex items-center justify-center mr-3 group-hover:border-opacity-60 transition-all duration-300">
                  <Handshake size={20} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="text-xl font-bold text-deep-navy">Integrity</h3>
              </div>
              <p className="text-rich-charcoal">Honest pricing, transparent communication, and reliable service you can count on.</p>
            </div>
            <div className="bg-soft-beige p-6 rounded-lg group hover:shadow-lg transition-all duration-300">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 border-2 border-premium-gold border-opacity-40 rounded-full flex items-center justify-center mr-3 group-hover:border-opacity-60 transition-all duration-300">
                  <Heart size={20} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="text-xl font-bold text-deep-navy">Community</h3>
              </div>
              <p className="text-rich-charcoal">Proudly supporting local families and businesses throughout the Greater Toronto Area.</p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <FAQ />

      {/* Guarantee Section */}
      <section className="py-16 bg-soft-beige">
        <div className="container mx-auto text-center max-w-4xl">
          <h2 className="text-3xl font-bold text-deep-navy mb-8">Our Comprehensive Guarantee</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-md group hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-16 h-16 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mr-4 group-hover:border-opacity-50 transition-all duration-300">
                  <ShieldCheck size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="text-2xl font-bold text-premium-gold">Workmanship Warranty</h3>
              </div>
              <p className="text-rich-charcoal mb-4">
                We stand behind every installation with our comprehensive workmanship warranty. If any issues arise due to installation defects, we&apos;ll fix them at no cost to you.
              </p>
              <ul className="text-left text-rich-charcoal space-y-2">
                <li className="flex items-center">
                  <CheckCircle size={16} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} className="mr-2" /> 5-year workmanship guarantee
                </li>
                <li className="flex items-center">
                  <CheckCircle size={16} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} className="mr-2" /> Free repairs for installation defects
                </li>
                <li className="flex items-center">
                  <CheckCircle size={16} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} className="mr-2" /> 24/7 emergency service available
                </li>
              </ul>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-md group hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-16 h-16 border-2 border-premium-gold border-opacity-30 rounded-full flex items-center justify-center mr-4 group-hover:border-opacity-50 transition-all duration-300">
                  <CheckCircle size={32} style={{color: 'var(--color-premium-gold)', strokeWidth: 2}} />
                </div>
                <h3 className="text-2xl font-bold text-premium-gold">Satisfaction Promise</h3>
              </div>
              <p className="text-rich-charcoal mb-4">
                Your complete satisfaction is our priority. We&apos;re not finished until you&apos;re 100% happy with your new fence.
              </p>
              <ul className="text-left text-rich-charcoal space-y-2">
                <li className="flex items-center">
                  <CheckCircle
                    className="w-4 h-4 stroke-current fill-none mr-2"
                    style={{color: 'var(--color-premium-gold)', strokeWidth: 2}}
                  /> 100% satisfaction guarantee
                </li>
                <li className="flex items-center">
                  <CheckCircle
                    className="w-4 h-4 stroke-current fill-none mr-2"
                    style={{color: 'var(--color-premium-gold)', strokeWidth: 2}}
                  /> Free consultations and estimates
                </li>
                <li className="flex items-center">
                  <CheckCircle
                    className="w-4 h-4 stroke-current fill-none mr-2"
                    style={{color: 'var(--color-premium-gold)', strokeWidth: 2}}
                  /> Clear communication throughout
                </li>
              </ul>
            </div>
          </div>
          
          <div className="mt-8 bg-deep-navy text-white p-6 rounded-lg">
            <h3 className="text-xl font-bold mb-2">Material Warranties Included</h3>
            <p>All materials come with manufacturer warranties ranging from 10-25 years depending on the product. We&apos;ll help you understand and utilize these warranties if needed.</p>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-premium-gold text-center">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold mb-4 text-deep-navy">Get Your Free Fencing Estimate Today</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto text-deep-navy">
            Ready to transform your property with a beautiful, durable fence? Contact us today for your free, no-obligation consultation and detailed estimate.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <CTA text="Request Your Free Quote" link="/contact" />
            <a 
              href="tel:************" 
              className="inline-block bg-deep-navy text-warm-off-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-opacity-90 transition-all"
            >
              Call: (*************
            </a>
          </div>
          <p className="text-sm mt-6 text-deep-navy">
            Licensed • Insured • Locally Owned • Serving the GTA Since 2009
          </p>
        </div>
      </section>
    </div>
    </>
  );
}
