import Link from 'next/link';

const Footer = () => {
  return (
    <footer className="bg-deep-navy text-warm-off-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          
          {/* Company Info Section */}
          <div className="lg:col-span-1">
            <div className="mb-6">
              <h3 className="text-2xl font-bold text-premium-gold mb-3">GTA Fencing</h3>
              <p className="text-sm leading-relaxed mb-4">
                Toronto's premier fence contractor with over 15+ years of experience. 
                Licensed, insured, and committed to exceptional quality and customer satisfaction.
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <span className="text-premium-gold mr-2">✓</span>
                  <span>Licensed & Insured</span>
                </div>
                <div className="flex items-center">
                  <span className="text-premium-gold mr-2">✓</span>
                  <span>15+ Years Experience</span>
                </div>
                <div className="flex items-center">
                  <span className="text-premium-gold mr-2">✓</span>
                  <span>Free Estimates</span>
                </div>
              </div>
            </div>
          </div>

          {/* Services Section */}
          <div>
            <h4 className="text-lg font-semibold text-premium-gold mb-4">Our Services</h4>
            <ul className="space-y-2 text-sm">
              <li><Link href="/services/wood-fencing" className="hover:text-premium-gold transition-colors duration-200">Wood Fencing</Link></li>
              <li><Link href="/services/vinyl-fencing" className="hover:text-premium-gold transition-colors duration-200">Vinyl Fencing</Link></li>
              <li><Link href="/services/chain-link-fencing" className="hover:text-premium-gold transition-colors duration-200">Chain Link Fencing</Link></li>
              <li><Link href="/services/pool-fencing" className="hover:text-premium-gold transition-colors duration-200">Pool Fencing</Link></li>
              <li><Link href="/services/fence-repair-services" className="hover:text-premium-gold transition-colors duration-200">Fence Repair</Link></li>
              <li><Link href="/services" className="text-premium-gold hover:text-white transition-colors duration-200">View All Services →</Link></li>
            </ul>
          </div>

          {/* Quick Links Section */}
          <div>
            <h4 className="text-lg font-semibold text-premium-gold mb-4">Quick Links</h4>
            <ul className="space-y-2 text-sm">
              <li><Link href="/" className="hover:text-premium-gold transition-colors duration-200">Home</Link></li>
              <li><Link href="/about" className="hover:text-premium-gold transition-colors duration-200">About Us</Link></li>
              <li><Link href="/locations" className="hover:text-premium-gold transition-colors duration-200">Service Areas</Link></li>
              <li><Link href="/blog" className="hover:text-premium-gold transition-colors duration-200">Blog</Link></li>
              <li><Link href="/contact" className="hover:text-premium-gold transition-colors duration-200">Contact</Link></li>
              <li><a href="https://www.google.com/maps/place/GTA+Fencing/@43.901344,-79.2986476,9z/data=!4m6!3m5!1s0x8a4021564033252b:0xea23a3c328e109d5!8m2!3d43.901344!4d-79.2986476!16s%2Fg%2F11tggq1x8f" target="_blank" rel="noopener noreferrer" className="hover:text-premium-gold transition-colors duration-200">Google Reviews</a></li>
            </ul>
          </div>

          {/* Contact & Social Section */}
          <div>
            <h4 className="text-lg font-semibold text-premium-gold mb-4">Get In Touch</h4>
            <div className="space-y-3 text-sm mb-6">
              <div className="flex items-start">
                <span className="text-premium-gold mr-2 mt-1">📞</span>
                <div>
                  <a href="tel:************" className="hover:text-premium-gold transition-colors duration-200 font-medium">
                    (*************
                  </a>
                  <p className="text-xs text-gray-300">Mon-Fri: 8AM-6PM</p>
                </div>
              </div>
              <div className="flex items-start">
                <span className="text-premium-gold mr-2 mt-1">✉️</span>
                <a href="mailto:<EMAIL>" className="hover:text-premium-gold transition-colors duration-200">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-start">
                <span className="text-premium-gold mr-2 mt-1">📍</span>
                <span>Greater Toronto Area</span>
              </div>
            </div>

            {/* CTA Button */}
            <Link href="/contact" className="inline-block bg-bright-orange-red text-white px-6 py-3 rounded-lg font-semibold text-sm hover:bg-opacity-90 transition-all duration-200 transform hover:scale-105 shadow-lg mb-6">
              Get Free Quote
            </Link>

            {/* Social Media Icons */}
            <div>
              <h5 className="text-sm font-medium text-premium-gold mb-3">Follow Us</h5>
              <div className="flex space-x-4">
                <a href="https://www.facebook.com/gtafencing" target="_blank" rel="noopener noreferrer" className="text-warm-off-white hover:text-premium-gold transition-colors duration-200">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                  </svg>
                </a>
                <a href="https://x.com/FencingGta" target="_blank" rel="noopener noreferrer" className="text-warm-off-white hover:text-premium-gold transition-colors duration-200">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                </a>
                <a href="https://www.youtube.com/@gtafencecompany" target="_blank" rel="noopener noreferrer" className="text-warm-off-white hover:text-premium-gold transition-colors duration-200">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" clipRule="evenodd" />
                  </svg>
                </a>
                <a href="https://www.pinterest.com/gtafencing/" target="_blank" rel="noopener noreferrer" className="text-warm-off-white hover:text-premium-gold transition-colors duration-200">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.**************.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.987C24.007 5.367 18.641.001 12.017.001z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-600">
        <div className="container mx-auto px-6 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-gray-300">
              <p>&copy; {new Date().getFullYear()} GTA Fencing Company. All Rights Reserved.</p>
            </div>
            <div className="flex flex-wrap justify-center md:justify-end space-x-6 text-sm">
              <Link href="/privacy" className="text-gray-300 hover:text-premium-gold transition-colors duration-200">Privacy Policy</Link>
              <Link href="/terms" className="text-gray-300 hover:text-premium-gold transition-colors duration-200">Terms of Service</Link>
              <a href="https://www.google.com/maps/place/GTA+Fencing/@43.901344,-79.2986476,9z/data=!4m6!3m5!1s0x8a4021564033252b:0xea23a3c328e109d5!8m2!3d43.901344!4d-79.2986476!16s%2Fg%2F11tggq1x8f" target="_blank" rel="noopener noreferrer" className="text-gray-300 hover:text-premium-gold transition-colors duration-200">
                Google Business
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
